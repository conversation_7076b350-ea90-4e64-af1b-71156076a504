import React, { useEffect, useMemo, useRef } from 'react';
import {
  <PERSON><PERSON>,
  BlockView,
  BottomView,
  ColorsV2,
  ConditionView,
  HomeMovingProgressPostTaskType,
  IconAssets,
  IconImage,
  IHomeDetail,
  IUserLocation,
  KeyboardAware,
  LocationItem,
  Maybe,
  PrimaryButton,
  ProcessButton,
  SizedBox,
  Spacing,
} from '@btaskee/design-system';
import { usePostTaskStore } from '@store';
import { debounce } from 'lodash-es';

import {
  ChooseAddressModal,
  ConfirmChangeHomeTypeContent,
  LocationEmpty,
} from '@components';
import { useAppNavigation, useI18n, usePostTaskHomeMoving } from '@hooks';

import { ChooseHomeType } from './components/ChooseHomeType';
import { ChooseOptionAddress } from './components/ChooseOptionAddress';
import { styles } from './styles';

export type AddressDetailHomeMovingProps = {
  step: HomeMovingProgressPostTaskType;
  title?: string;
  location?: IUserLocation;
  homeDetail?: IHomeDetail;
  blackListLocation?: Maybe<string>[];
  titleButtonNext?: string;
  labelOptionClean?: string;
  onChooseLocation?: (location?: Maybe<IUserLocation>) => void;
};

export const AddressDetailHomeMoving = ({
  step,
}: AddressDetailHomeMovingProps) => {
  const navigation = useAppNavigation();
  const { t } = useI18n();
  const {
    getDetailSettingHomeType,
    getContentByStep,
    getDataPostTaskMoving,
    onChangeLocation,
    getBlackListLocation,
    onNextStep,
    checkIsSupportCity,
    onAddLocation,
    getLocations,
    getIsShowAddIsInBuilding,
    getDetailSettingHomeType,
  } = usePostTaskHomeMoving();

  // Use Zustand store instead of Redux
  const {
    currentStep,
    isInBuilding,
    oldHomeDetail,
    temptStairsTransportStep1,
    setIsInBuilding,
    setHomeDetail,
    setTemptStairsTransportStep1,
    resetState,
  } = usePostTaskStore();

  const dataPostTask = useMemo(() => {
    return getDataPostTaskMoving(step);
  }, [getDataPostTaskMoving, step]);

  const contentPostTask = useMemo(() => {
    return getContentByStep(step);
  }, [getContentByStep, step]);

  const currentHomeDetail = useMemo(() => {
    return dataPostTask?.homeDetail;
  }, [dataPostTask?.homeDetail]);

  const isStep2 = useMemo(() => {
    return step === HomeMovingProgressPostTaskType.Step2;
  }, [step]);

  const isFocusStep2 = useMemo(() => {
    return isStep2 && currentStep === step;
  }, [currentStep, isStep2, step]);

  const currentCity = useMemo(() => {
    return currentHomeDetail?.addressDetail?.taskPlace?.city;
  }, [currentHomeDetail?.addressDetail?.taskPlace?.city]);

  const nameHomeTypeMoving = useMemo(() => {
    return currentHomeDetail?.homeType?.name;
  }, [currentHomeDetail?.homeType?.name]);

  const isSupportCity = useMemo(() => {
    return checkIsSupportCity(currentCity);
  }, [checkIsSupportCity, currentCity]);

  const detailHomeType = useMemo(() => {
    return getDetailSettingHomeType({
      city: currentCity,
      nameHomeType: nameHomeTypeMoving,
    });
  }, [currentCity, getDetailSettingHomeType, nameHomeTypeMoving]);

  const isDisableButton = useMemo(() => {
    return !currentHomeDetail?.homeType?.type?.name && !isFocusStep2;
  }, [currentHomeDetail?.homeType?.type?.name, isFocusStep2]);

  const blackListLocation = useMemo(() => {
    return getBlackListLocation();
  }, [getBlackListLocation]);

  const locations = useMemo(() => {
    return getLocations(blackListLocation?.address);
  }, [blackListLocation?.address, getLocations]);

  const contentContainerStyle = useMemo(() => {
    return { paddingBottom: isStep2 ? 200 : Spacing.SPACE_64 };
  }, [isStep2]);

  const isShowAddIsInBuilding = useMemo(() => {
    return getIsShowAddIsInBuilding();
  }, [getIsShowAddIsInBuilding]);

  const onAddNewAddressDebounce = useRef(debounce(onAddLocation, 500)).current;

  useEffect(() => {
    if (isFocusStep2 && !currentHomeDetail?.addressDetail?.address) {
      onAddNewAddressDebounce({
        title: contentPostTask.title,
        step,
        isShowAddIsInBuilding,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    currentHomeDetail?.addressDetail?.address,
    currentStep,
    isFocusStep2,
    step,
  ]);

  const _onChangeLocation = (location?: Maybe<IUserLocation>) => {
    onChangeLocation?.({ step, location });
  };

  const _onAddNewAddress = () => {
    onAddLocation({
      title: contentPostTask.title,
      step,
      isShowAddIsInBuilding,
    });
  };

  const onChangeAddress = () => {
    if (isInBuilding) {
      showConfirmChangeHomeType();
      return;
    }

    // Nếu là step 1 thì mở modal list location
    if (step === HomeMovingProgressPostTaskType.Step1 && locations?.length) {
      // modalChooseAddressRef?.current?.open?.();
      Alert.alert.open({
        title: t('CHOOSE_ADDRESS'),
        message: (
          <ChooseAddressModal
            locations={locations}
            onChooseAddress={_onChangeLocation}
            onAddNewAddress={_onAddNewAddress}
          />
        ),
      });
      return;
    }
    _onAddNewAddress();
  };

  const showConfirmChangeHomeType = () => {
    // Use Alert from design system instead of AlertHolder
    Alert.alert.open({
      title: t('CONFIRM_CHANGE'),
      titleStyle: { fontSize: 18 },
      message: (
        <ConfirmChangeHomeTypeContent
          onConfirm={() => {
            // Nếu ở step "Chọn nơi đi" thì reset lại state của post task home moving
            if (step === HomeMovingProgressPostTaskType.Step1) {
              resetState();
              return;
            }
            // Trường hợp nếu ở step chọn nơi đến
            // Nếu biến tempt option "vận chuyển thang bộ" có giá trị thì set lại cho nơi đi
            if (temptStairsTransportStep1) {
              setTemptStairsTransportStep1(null);

              setHomeDetail({
                step: HomeMovingProgressPostTaskType.Step1,
                homeDetail: {
                  ...oldHomeDetail,
                  options: [
                    temptStairsTransportStep1,
                    ...(oldHomeDetail?.options || []),
                  ],
                },
              });
            }

            // Set option chuyển cùng tòa nhà = false
            setIsInBuilding(false);

            // Clear newHomeDetail
            setHomeDetail({
              step: HomeMovingProgressPostTaskType.Step2,
              homeDetail: {},
            });
          }}
        />
      ),
    });
  };

  const onPressChangeAddress = () => {
    onChangeAddress();
  };

  const onNext = () => {
    onNextStep(step);
  };

  const goToMovingProgress = () => {
    // trackingServiceView({
    //   screenName: TrackingScreenNames.ProcessIntroduction,
    //   serviceName: SERVICES.HOME_MOVING,
    //   entryPoint:
    //     step === HomeMovingProgressPostTaskType.Step1
    //       ? TrackingScreenNames.CurrentLocation
    //       : TrackingScreenNames.NewLocation,
    // });

    // Navigate to the global route for HomeMovingDescriptionProgress
    // @ts-ignore - This route exists in the main navigation but not in local RouteName
    navigation.navigate('HomeMovingDescriptionProgress');
  };

  return (
    <>
      <BlockView style={styles.container}>
        <KeyboardAware
          testID="AddressDetailHomeMovingScrollView"
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{
            ...contentContainerStyle,
            paddingHorizontal: Spacing.SPACE_16,
          }}
        >
          <BlockView margin={{ top: Spacing.SPACE_08 }}>
            <ConditionView
              condition={Boolean(currentHomeDetail?.addressDetail)}
              viewTrue={
                <LocationItem
                  testIDs={{
                    editBtn: 'editBtnHomeDetail',
                  }}
                  shortAddress={currentHomeDetail?.addressDetail?.shortAddress}
                  address={currentHomeDetail?.addressDetail?.address}
                  onPressUpdate={onPressChangeAddress}
                />
              }
              viewFalse={<LocationEmpty testID="locationEmptyHomeMoving" />}
            />
            <PrimaryButton
              testID="choseOtherAddressBtnHomeMoving"
              title={t('CHOOSE_ADDRESS_OTHER')}
              titleProps={{
                bold: false,
                color: ColorsV2.green500,
                margin: { left: Spacing.SPACE_08 },
              }}
              left={
                <IconImage
                  source={IconAssets.icPlus}
                  color={ColorsV2.green500}
                />
              }
              onPress={onPressChangeAddress}
              style={styles.addAddressButton}
            />
          </BlockView>
          <ConditionView
            condition={isSupportCity}
            viewTrue={
              <ConditionView
                condition={!detailHomeType?.name}
                viewTrue={<ChooseHomeType step={step} />}
                viewFalse={<ChooseOptionAddress step={step} />}
              />
            }
            viewFalse={
              <LocationEmpty
                testID="notSupportLocationHomeMoving"
                label={t('MOVING.LABEL_NOT_SUPPORT_LOCATION')}
              />
            }
          />
          <SizedBox height={30} />
          <ProcessButton
            label={t('MOVING_PROCESS')}
            onPress={goToMovingProgress}
          />
        </KeyboardAware>
      </BlockView>
      <ConditionView
        condition={!isStep2}
        viewTrue={
          <BottomView margin={{ horizontal: Spacing.SPACE_16 }}>
            <PrimaryButton
              testID="btnNextMoving"
              title={contentPostTask?.labelButton || ''}
              onPress={onNext}
              disabled={isDisableButton}
            />
          </BottomView>
        }
      />
    </>
  );
};

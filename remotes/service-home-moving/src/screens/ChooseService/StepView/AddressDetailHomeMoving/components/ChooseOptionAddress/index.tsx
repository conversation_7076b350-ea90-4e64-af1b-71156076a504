import React, { useMemo } from 'react';
import {
  BlockView,
  HomeMovingProgressPostTaskType,
  Spacing,
} from '@btaskee/design-system';
import { usePostTaskStore } from '@store';

import { OptionItem, Section } from '@components';
import { useI18n, usePostTaskHomeMoving } from '@hooks';

import { styles } from './styles';

type ChooseOptionAddressProps = {
  step: HomeMovingProgressPostTaskType;
};

export const ChooseOptionAddress = ({ step }: ChooseOptionAddressProps) => {
  const { t } = useI18n();
  const {
    getDetailSettingHomeType,
    getDataPostTaskMoving,
    getContentOptionHomeType,
    getIconOptionHomeType,
    getContentByHomeType,
  } = usePostTaskHomeMoving();

  const { currentStep } = usePostTaskStore();

  const dataPostTask = useMemo(() => {
    return getDataPostTaskMoving(step);
  }, [getDataPostTaskMoving, step]);

  const currentHomeDetail = useMemo(() => {
    return dataPostTask.homeDetail;
  }, [dataPostTask.homeDetail]);

  const currentCity = useMemo(() => {
    return currentHomeDetail?.addressDetail?.taskPlace?.city;
  }, [currentHomeDetail?.addressDetail?.taskPlace?.city]);

  const nameHomeTypeMoving = useMemo(() => {
    return currentHomeDetail?.homeType?.name;
  }, [currentHomeDetail?.homeType?.name]);

  const detailHomeType = useMemo(() => {
    return getDetailSettingHomeType({
      city: currentCity,
      nameHomeType: nameHomeTypeMoving,
    });
  }, [currentCity, getDetailSettingHomeType, nameHomeTypeMoving]);

  const isFocusStep = useMemo(() => {
    return currentStep === step;
  }, [currentStep, step]);

  const optionsByHomeType = useMemo(() => {
    return detailHomeType?.optionsByHomeType || [];
  }, [detailHomeType?.optionsByHomeType]);

  if (!isFocusStep || !detailHomeType?.name || !optionsByHomeType.length) {
    return null;
  }

  return (
    <Section
      title={t('CHOOSE_OPTION_ADDRESS')}
      margin={{ top: Spacing.SPACE_24 }}
    >
      <BlockView margin={{ top: Spacing.SPACE_16 }}>
        {optionsByHomeType.map((option, index) => {
          const content = getContentOptionHomeType(option.name);
          const iconSource = getIconOptionHomeType(option.name);

          return (
            <OptionItem
              key={`option-${index}`}
              testID={`option${option.name}Btn`}
              option={option}
              step={step}
              title={content.title}
              subTitle={content.subTitle}
              iconSource={iconSource}
              style={styles.optionItem}
            />
          );
        })}
      </BlockView>
    </Section>
  );
};
